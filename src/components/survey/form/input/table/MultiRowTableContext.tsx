import { useGetUtrvVariationsQuery } from '@api/utrv';
import { InputColumn } from '@components/survey/form/input/table/InputInterface';
import { VariationWarningMessage } from '@components/survey/question/variation/VariationWarningMessage';
import { getUtrvVariationWarnings } from '@components/survey/utils/input';
import { hasOverriddenVariations } from '@features/question-configuration';
import { InitiativeUniversalTracker } from '@g17eco/types/initiativeUniversalTracker';
import { UniversalTrackerBase } from '@models/UniversalTracker';
import { skipToken } from '@reduxjs/toolkit/query';
import { createContext, useCallback, useContext } from 'react';

interface ContextProps {
  getColumnVariationWarningMessage: (valueListCode: string) => React.ReactNode;
  formRow: InputColumn[] | undefined;
}

const MultiRowTableContext = createContext<ContextProps>({
  getColumnVariationWarningMessage: () => null,
  formRow: undefined,
});

export const useMultiRowTableContext = () => {
  return useContext(MultiRowTableContext);
};

interface Props {
  children: JSX.Element;
  utr: UniversalTrackerBase;
  aggregatedTableData?: InputColumn[][];
  utrvId?: string;
  initiativeUtr?: InitiativeUniversalTracker;
  formRow: InputColumn[];
}

export const MultiRowTableProvider = ({
  children,
  aggregatedTableData,
  utr,
  initiativeUtr,
  utrvId,
  formRow,
}: Props) => {
  const hasVariations = initiativeUtr && hasOverriddenVariations(initiativeUtr);
  const { data: utrvVariations } = useGetUtrvVariationsQuery(hasVariations && utrvId ? { utrvId } : skipToken);

  const getColumnVariationWarningMessage = useCallback(
    (valueListCode: string) => {
      const variationWarnings = getUtrvVariationWarnings({
        utr,
        utrvVariations,
        currentInputData: {
          aggregatedTableData,
          table: { editRowId: -1, rows: [] },
          valueData: { data: {} },
        },
      });

      const variationWarning = variationWarnings.find((warning) => warning.valueListCode === valueListCode);
      if (!variationWarning) {
        return null;
      }
      return <VariationWarningMessage variationWarning={variationWarning} />;
    },
    [aggregatedTableData, utr, utrvVariations],
  );

  return (
    <MultiRowTableContext.Provider value={{ getColumnVariationWarningMessage, formRow }}>
      {children}
    </MultiRowTableContext.Provider>
  );
};
